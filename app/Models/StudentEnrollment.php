<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use App\Services\GeneralSettingsService;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model; // Import Builder
use Illuminate\Database\Eloquent\SoftDeletes; // Import Cache
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;

// Add this import

/**
 * Class StudentEnrollment
 *
 * @property int $id
 * @property string $student_id
 * @property string $course_id
 * @property string $status
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property int|null $semester
 * @property int|null $academic_year
 * @property string|null $school_year
 * @property string|null $deleted_at
 * @property float|null $downpayment
 * @property string|null $remarks
 */
final class StudentEnrollment extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = "student_enrollment";

    protected $fillable = [
        "student_id",
        "course_id",
        "status",
        "semester",
        "academic_year",
        "school_year",
        "downpayment",
        "remarks",
    ];

    protected $casts = [
        "id" => "integer",
        "semester" => "integer",
        "academic_year" => "integer",
        "downpayment" => "float",
        "created_at" => "datetime",
        "updated_at" => "datetime",
        "deleted_at" => "datetime",
    ];

    private array $dates = ["deleted_at"];

    public static function boot(): void
    {
        parent::boot();

        self::creating(function (self $model): void {
            $settings = GeneralSetting::first();
            $model->status = "Pending";
            $model->school_year = $settings->getSchoolYearString();
            $model->semester = $settings->semester;
        });

        // delete also the subjects enrolled
        self::forceDeleted(function (self $model): void {
            $model->subjectsEnrolled()->forceDelete();
            $model->studentTuition()->forceDelete();
        });
    }

    public function signature()
    {
        return $this->morphOne(EnrollmentSignature::class, "enrollment");
    }

    public function student()
    {
        return $this->belongsTo(Student::class, "student_id", "id")
            ->withoutGlobalScopes()
            ->withDefault();
    }

    public function getStudentNameAttribute(): string
    {
        return $this->student->full_name;
    }

    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    public function subjectsEnrolled()
    {
        return $this->hasMany(SubjectEnrollment::class, "enrollment_id", "id");
    }

    public function studentTuition()
    {
        return $this->hasOne(StudentTuition::class, "enrollment_id", "id");
    }

    public function resources()
    {
        return $this->morphMany(Resource::class, "resourceable");
    }

    /**
     * Get transactions for this enrollment through the student
     */
    public function transactions()
    {
        return $this->student->Transaction();
    }

    /**
     * Get student transactions for this enrollment through the student
     */
    public function studentTransactions()
    {
        return $this->student->StudentTransactions();
    }

    public function getAssessmentPathAttribute(): string
    {
        return $this->resources()
            ->where("type", "assessment")
            ->latest()
            ->first()->file_path;
    }

    public function getCertificatePathAttribute(): string
    {
        return $this->resources()
            ->where("type", "certificate")
            ->latest()
            ->first()->file_path;
    }

    public function getAssessmentUrlAttribute(): string
    {
        $resource = $this->resources()
            ->where("type", "assessment")
            ->latest()
            ->first();
        if (!$resource) {
            return "";
        }

        // Use asset helper instead of Storage::url
        try {
            return asset("storage/" . mb_ltrim($resource->file_path, "/"));
        } catch (Exception) {
            return "";
        }
    }

    public function getCertificateUrlAttribute(): string
    {
        $resource = $this->resources()
            ->where("type", "certificate")
            ->latest()
            ->first();
        if (!$resource) {
            return "";
        }

        // Use asset helper instead of Storage::url
        try {
            return asset("storage/" . mb_ltrim($resource->file_path, "/"));
        } catch (Exception) {
            return "";
        }
    }

    /**
     * Scope a query to only include enrollments for the current school year and semester.
     */
    public function scopeCurrentAcademicPeriod(Builder $query): Builder
    {
        // Use the GeneralSettingsService to get effective settings
        /** @var GeneralSettingsService $settingsService */
        $settingsService = app(GeneralSettingsService::class);

        $schoolYear = $settingsService->getCurrentSchoolYearString(); // e.g., "2024 - 2025"
        $semester = $settingsService->getCurrentSemester(); // integer (1 or 2)

        return $query
            ->where("school_year", $schoolYear)
            ->where("semester", $semester);
    }
}
