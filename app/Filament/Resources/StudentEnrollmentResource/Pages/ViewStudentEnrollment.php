<?php

declare(strict_types=1);

namespace App\Filament\Resources\StudentEnrollmentResource\Pages;

use App\Enums\AcademicYear;
use App\Enums\EnrollStat;
use App\Filament\Resources\StudentEnrollmentResource;
use App\Models\GeneralSetting;
use App\Models\StudentEnrollment;
use App\Models\Transaction;
use App\Models\StudentTransaction;
use App\Services\EnrollmentService;
use Exception;
use Filament\Actions;
use Filament\Actions\ActionGroup;
use Filament\Forms\Components\KeyValue;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Infolists\Components\Actions\Action as InfolistAction;
use Filament\Infolists\Components\Actions as InfolistActions;
use Filament\Infolists\Components\Fieldset;
use Filament\Infolists\Components\RepeatableEntry;
use Filament\Infolists\Components\Section as InfolistSection;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\ViewEntry;
use Filament\Support\Enums\FontWeight;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;
use Icetalker\FilamentTableRepeatableEntry\Infolists\Components\TableRepeatableEntry;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Saade\FilamentAutograph\Forms\Components\Enums\DownloadableFormat;
use Saade\FilamentAutograph\Forms\Components\SignaturePad;

final class ViewStudentEnrollment extends ViewRecord
{
    protected static string $resource = StudentEnrollmentResource::class;

    protected function resolveRecord($key): StudentEnrollment
    {
        // Ensure we can load soft-deleted records for viewing
        return StudentEnrollment::withTrashed()->with([
            'studentTuition',
            'student',
            'subjectsEnrolled',
            'signature',
            'resources',
            'enrollmentTransactions.studentTransactions',
            'enrollmentStudentTransactions.transaction'
        ])->findOrFail($key);
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Fieldset::make("Student Information")
                    ->columns(3)
                    ->columnSpan(3)
                    ->schema([
                        TextEntry::make("student_id")
                            ->badge()
                            ->copyable()
                            ->icon("phosphor-clipboard")
                            ->label("Student ID"),

                        TextEntry::make("student.full_name")->label(
                            "Student Full name"
                        ),
                        TextEntry::make("student.email")->label(
                            "Student Email"
                        ),
                        TextEntry::make("student.academic_year")
                            ->label("Year Level")
                            ->formatStateUsing(
                                fn($state) => match ($state) {
                                    1 => AcademicYear::first,
                                    2 => AcademicYear::second,
                                    3 => AcademicYear::third,
                                    4 => AcademicYear::fourth,
                                    default => $state,
                                }
                            ),
                        TextEntry::make("student.course.code")
                            ->badge()
                            ->label("Course"),
                        InfolistSection::make("Subjects Enrolled")
                            ->collapsed()
                            ->schema([
                                TableRepeatableEntry::make("subjectsEnrolled")
                                    ->columns(4)
                                    ->schema([
                                        TextEntry::make("subject.code")->label(
                                            "Subject Code"
                                        ),
                                        TextEntry::make("subject.title")
                                            ->label("Subject Title")
                                            ->words(3),
                                        TextEntry::make("subject.units")->label(
                                            "Units"
                                        ),
                                        TextEntry::make("school_year")->label(
                                            "School Year"
                                        ),
                                        TextEntry::make("semester")->label(
                                            "Semester"
                                        ),
                                    ])
                                    ->label("Subjects Enrolled"),
                            ]),

                        InfolistSection::make("Enrollee's Schedule")
                            ->collapsed()
                            ->schema([
                                ViewEntry::make("classSchedule")->view(
                                    "infolists.components.enrollee-sched"
                                ),
                            ]),
                    ]),
                Fieldset::make("Enrollee's Status")
                    ->columnSpan(1)
                    ->schema([
                        TextEntry::make("status")
                            ->label("Status")
                            ->badge()
                            ->columnSpanFull()
                            ->icon(
                                fn($record): string => match ($record->status) {
                                    EnrollStat::Pending->value
                                        => "phosphor-x-fill",
                                    EnrollStat::VerifiedByDeptHead->value
                                        => "phosphor-check-square-offset",
                                    EnrollStat::VerifiedByCashier->value
                                        => "phosphor-check-circle",
                                    default => "phosphor-question-mark-light",
                                }
                            )
                            ->color(
                                fn($record): string => match ($record->status) {
                                    EnrollStat::Pending->value => "warning",
                                    EnrollStat::VerifiedByDeptHead->value
                                        => "success",
                                    EnrollStat::VerifiedByCashier->value
                                        => "primary",
                                }
                            ),
                        ViewEntry::make("signature.depthead_signature")
                            ->label("Department head Signature")
                            ->visible(
                                fn($record): bool => isset(
                                    $record->signature->depthead_signature
                                )
                            )
                            ->columnSpanFull()
                            ->view("infolists.components.signature-view"),
                        ViewEntry::make("signature.registrar_signature")
                            ->label("Registrar Signature")
                            ->visible(
                                fn($record): bool => isset(
                                    $record->signature->registrar_signature
                                )
                            )
                            ->view("infolists.components.signature-view"),
                        ViewEntry::make("signature.cashier_signature")
                            ->label("Cashier Signature")
                            ->visible(
                                fn($record): bool => isset(
                                    $record->signature->cashier_signature
                                )
                            )
                            ->view("infolists.components.signature-view"),
                        TextEntry::make("studentTuition.discount")
                            ->label("Discount")
                            ->columnSpanFull()
                            ->prefix("%"),
                        TextEntry::make("studentTuition.total_lectures")
                            ->label("Total Lecture Fee")
                            ->columnSpanFull()
                            ->prefix("₱"),
                        TextEntry::make("studentTuition.total_laboratory")
                            ->label("Total Laboratory Fee")
                            ->columnSpanFull()
                            ->prefix("₱"),
                        TextEntry::make(
                            "studentTuition.total_miscelaneous_fees"
                        )
                            ->label("Total Miscellaneous Fee")
                            ->columnSpanFull()
                            ->prefix("₱"),
                        TextEntry::make("studentTuition.overall_tuition")
                            ->label("Overall Tuition Fee")
                            ->columnSpanFull()
                            ->prefix("₱"),
                        TextEntry::make("studentTuition.downpayment")
                            ->label("Down Payment")
                            ->columnSpanFull()
                            ->prefix("₱")
                            ->tooltip("Reapply Transaction")
                            ->suffixAction(
                                InfolistAction::make("Reapply Downpayment")
                                    ->icon("heroicon-m-arrow-uturn-left")
                                    ->requiresConfirmation()
                                    ->action(function ($record) {
                                        DB::beginTransaction();
                                        try {
                                            $tuition = $record->studentTuition;
                                            $previous_balance =
                                                $tuition->total_balance;
                                            $tuition->total_balance -=
                                                $tuition->downpayment;
                                            $tuition->save();
                                            Notification::make(
                                                "Successfully Reapplied Transaction"
                                            )
                                                ->success()
                                                ->body(
                                                    "Balance: ₱" .
                                                        $tuition->total_balance .
                                                        " has been updated" .
                                                        "Previous Balance: ₱" .
                                                        $previous_balance
                                                )
                                                ->send();
                                            DB::commit();
                                        } catch (Exception $e) {
                                            DB::rollBack();
                                            Notification::make(
                                                "Failed to Reapply Transaction"
                                            )
                                                ->danger()
                                                ->body($e->getMessage())
                                                ->send();
                                        }

                                        return $record;
                                    })
                            ),
                        TextEntry::make("studentTuition.total_balance")
                            ->label("Balance")
                            ->columnSpanFull()
                            ->prefix("₱"),
                    ]),

                InfolistSection::make("Transaction Details")
                    ->collapsed()
                    ->columnSpanFull()
                    ->headerActions([
                        InfolistAction::make("createTransaction")
                            ->label("Create New Transaction")
                            ->icon("heroicon-m-plus")
                            ->color('primary')
                            ->modalHeading("Create New Transaction")
                            ->modalDescription(function ($record) {
                                return "Create a new transaction for this enrollment ({$record->school_year}, Semester {$record->semester})";
                            })
                            ->form([
                                TextInput::make("description")
                                    ->label("Description")
                                    ->required()
                                    ->default(function () {
                                        $enrollment = $this->record;
                                        return "Payment for enrollment ({$enrollment->school_year}, Semester {$enrollment->semester})";
                                    })
                                    ->maxLength(255),
                                TextInput::make("invoicenumber")
                                    ->label("Invoice/O.R. Number")
                                    ->required()
                                    ->maxLength(255)
                                    ->placeholder("Enter invoice number"),
                                KeyValue::make("settlements")
                                    ->label("Settlements")
                                    ->columnSpanFull()
                                    ->helperText("Enter the settlement amounts for different fees")
                                    ->default([
                                        "registration_fee" => 0,
                                        "tuition_fee" => 0,
                                        "miscelanous_fee" => 0,
                                        "diploma_or_certificate" => 0,
                                        "transcript_of_records" => 0,
                                        "certification" => 0,
                                        "special_exam" => 0,
                                        "others" => 0,
                                    ])
                                    ->reorderable()
                                    ->editableKeys(false)
                                    ->keyLabel("Particulars")
                                    ->deletable(false)
                                    ->addable(false)
                                    ->valueLabel("Amounts")
                                    ->required(),
                                Select::make("status")
                                    ->label("Status")
                                    ->options([
                                        'pending' => 'Pending',
                                        'completed' => 'Completed',
                                        'cancelled' => 'Cancelled',
                                    ])
                                    ->default('completed')
                                    ->required(),
                            ])
                            ->action(function (array $data) {
                                try {
                                    DB::beginTransaction();

                                    $enrollment = $this->record;
                                    $student = $enrollment->student;

                                    // Create the transaction
                                    $transaction = Transaction::create([
                                        'description' => $data['description'],
                                        'status' => $data['status'],
                                        'transaction_date' => now(),
                                        'invoicenumber' => $data['invoicenumber'],
                                        'settlements' => $data['settlements'],
                                    ]);

                                    // Create the student transaction relationship
                                    $totalAmount = array_sum(array_values($data['settlements']));
                                    StudentTransaction::create([
                                        'student_id' => $student->id,
                                        'transaction_id' => $transaction->id,
                                        'amount' => $totalAmount,
                                        'status' => $data['status'],
                                    ]);

                                    DB::commit();

                                    Notification::make()
                                        ->title('Transaction Created Successfully')
                                        ->body("Transaction #{$transaction->transaction_number} created with total amount of ₱" . number_format($totalAmount, 2))
                                        ->success()
                                        ->send();

                                } catch (Exception $e) {
                                    DB::rollBack();
                                    Notification::make()
                                        ->title('Transaction Creation Failed')
                                        ->body($e->getMessage())
                                        ->danger()
                                        ->send();
                                }
                            })
                    ])
                    ->schema([
                        RepeatableEntry::make("enrollmentTransactions")
                            ->label("Enrollment Transactions")
                            ->visible(fn ($record) => $record->enrollmentTransactions->count() > 0)
                            ->schema([
                                TextEntry::make("transaction_number")
                                    ->label("Transaction #")
                                    ->badge()
                                    ->color('primary')
                                    ->weight(FontWeight::Bold)
                                    ->copyable(),
                                TextEntry::make("invoicenumber")
                                    ->label("Invoice/O.R. Number")
                                    ->badge()
                                    ->color('success')
                                    ->copyable()
                                    ->suffixAction(
                                        InfolistAction::make("editInvoice")
                                            ->icon("heroicon-m-pencil-square")
                                            ->tooltip("Edit Invoice Number")
                                            ->color('warning')
                                            ->modalHeading("Edit Invoice Number")
                                            ->modalDescription("Update the invoice/O.R. number for this transaction")
                                            ->form([
                                                TextInput::make("invoicenumber")
                                                    ->label("Invoice/O.R. Number")
                                                    ->required()
                                                    ->maxLength(255)
                                                    ->placeholder("Enter invoice number")
                                            ])
                                            ->fillForm(fn ($record) => [
                                                'invoicenumber' => $record->invoicenumber
                                            ])
                                            ->action(function (array $data, $record) {
                                                try {
                                                    $record->update([
                                                        'invoicenumber' => $data['invoicenumber']
                                                    ]);

                                                    Notification::make()
                                                        ->title('Invoice Number Updated')
                                                        ->body("Invoice number updated to: {$data['invoicenumber']}")
                                                        ->success()
                                                        ->send();

                                                } catch (Exception $e) {
                                                    Notification::make()
                                                        ->title('Update Failed')
                                                        ->body($e->getMessage())
                                                        ->danger()
                                                        ->send();
                                                }
                                            })
                                    ),
                                TextEntry::make("description")
                                    ->label("Description")
                                    ->limit(50),
                                TextEntry::make("status")
                                    ->label("Status")
                                    ->badge()
                                    ->color(fn (string $state): string => match ($state) {
                                        'completed' => 'success',
                                        'pending' => 'warning',
                                        'cancelled' => 'danger',
                                        default => 'gray',
                                    }),
                                TextEntry::make("total_amount")
                                    ->label("Total Amount")
                                    ->prefix("₱")
                                    ->weight(FontWeight::Bold)
                                    ->color('success')
                                    ->suffixAction(
                                        InfolistAction::make("editDownpayment")
                                            ->icon("heroicon-m-banknotes")
                                            ->tooltip("Update Downpayment")
                                            ->color('success')
                                            ->modalHeading("Update Downpayment")
                                            ->modalDescription("Update the downpayment amount and apply it to the student's tuition balance")
                                            ->form(function ($record) {
                                                $enrollment = $this->record; // Get the current enrollment record
                                                $currentDownpayment = $enrollment->studentTuition?->downpayment ?? 0;

                                                return [
                                                    TextInput::make("downpayment")
                                                        ->label("New Downpayment Amount")
                                                        ->required()
                                                        ->numeric()
                                                        ->prefix("₱")
                                                        ->step(0.01)
                                                        ->minValue(0)
                                                        ->default($currentDownpayment)
                                                        ->helperText("Current downpayment: ₱" . number_format($currentDownpayment, 2)),
                                                    TextInput::make("transaction_description")
                                                        ->label("Transaction Description")
                                                        ->default("Downpayment update for enrollment")
                                                        ->maxLength(255)
                                                ];
                                            })
                                            ->action(function (array $data, $record) {
                                                try {
                                                    DB::beginTransaction();

                                                    $enrollment = $this->record;
                                                    $tuition = $enrollment->studentTuition;

                                                    if (!$tuition) {
                                                        throw new Exception('No tuition record found for this enrollment');
                                                    }

                                                    $oldDownpayment = $tuition->downpayment ?? 0;
                                                    $newDownpayment = (float) $data['downpayment'];
                                                    $difference = $newDownpayment - $oldDownpayment;

                                                    // Update tuition record
                                                    $tuition->update([
                                                        'downpayment' => $newDownpayment,
                                                        'total_balance' => $tuition->total_balance - $difference
                                                    ]);

                                                    // Update the transaction settlements
                                                    $settlements = $record->settlements;
                                                    if (is_string($settlements)) {
                                                        $settlements = json_decode($settlements, true);
                                                    }
                                                    if (!is_array($settlements)) {
                                                        $settlements = [];
                                                    }

                                                    $settlements['tuition_fee'] = $newDownpayment;

                                                    $record->update([
                                                        'settlements' => $settlements,
                                                        'description' => $data['transaction_description'] ?? $record->description
                                                    ]);

                                                    DB::commit();

                                                    Notification::make()
                                                        ->title('Downpayment Updated Successfully')
                                                        ->body("Downpayment updated from ₱" . number_format($oldDownpayment, 2) . " to ₱" . number_format($newDownpayment, 2) . ". Balance adjusted by ₱" . number_format($difference, 2))
                                                        ->success()
                                                        ->send();

                                                } catch (Exception $e) {
                                                    DB::rollBack();
                                                    Notification::make()
                                                        ->title('Update Failed')
                                                        ->body($e->getMessage())
                                                        ->danger()
                                                        ->send();
                                                }
                                            })
                                    ),
                                TextEntry::make("transaction_date")
                                    ->label("Transaction Date")
                                    ->dateTime(),
                                TextEntry::make("created_at")
                                    ->label("Created")
                                    ->dateTime()
                                    ->since(),
                            ])
                            ->columns(3)
                            ->columnSpanFull(),

                        TextEntry::make("no_transactions")
                            ->label("")
                            ->visible(fn ($record) => $record->enrollmentTransactions->count() === 0)
                            ->formatStateUsing(fn ($record) => "No transactions found for this enrollment period ({$record->school_year}, Semester {$record->semester}).")
                            ->color('gray')
                            ->icon('heroicon-o-banknotes')
                            ->columnSpanFull(),
                    ]),

                Fieldset::make("Resources")
                    ->columnSpanFull()
                    ->schema([
                        RepeatableEntry::make("resources")
                            ->schema([
                                TextEntry::make("type")
                                    ->badge()
                                    ->color(
                                        fn(string $state): string => match (
                                            $state
                                        ) {
                                            "assessment" => "success",
                                            "certificate" => "warning",
                                            default => "gray",
                                        }
                                    )
                                    ->columnSpan(1),
                                TextEntry::make("file_name")
                                    ->label("File Name")
                                    ->copyable()
                                    ->columnSpan(2),
                                TextEntry::make("file_size")
                                    ->label("Size")
                                    ->formatStateUsing(
                                        fn($state): string => $state
                                            ? number_format($state / 1024, 2) .
                                                " KB"
                                            : "Unknown"
                                    )
                                    ->columnSpan(1),
                                TextEntry::make("created_at")
                                    ->label("Created")
                                    ->dateTime()
                                    ->since()
                                    ->columnSpan(1),
                                InfolistActions::make([
                                    InfolistAction::make("view")
                                        ->label("View")
                                        ->icon("heroicon-m-eye")
                                        ->color("primary")
                                        ->url(
                                            fn($record) => route(
                                                "view-resource",
                                                ["resource" => $record->id]
                                            )
                                        )
                                        ->openUrlInNewTab(),
                                    InfolistAction::make("download")
                                        ->label("Download")
                                        ->icon("heroicon-m-arrow-down-tray")
                                        ->color("info")
                                        ->url(
                                            fn($record) => route(
                                                "view-resource",
                                                ["resource" => $record->id]
                                            )
                                        )
                                        ->openUrlInNewTab(),
                                    InfolistAction::make("delete")
                                        ->label("Delete")
                                        ->icon("heroicon-m-trash")
                                        ->color("danger")
                                        ->requiresConfirmation()
                                        ->modalHeading("Delete Resource")
                                        ->modalDescription(
                                            "Are you sure you want to delete this resource? This action cannot be undone."
                                        )
                                        ->action(function ($record): void {
                                            try {
                                                // Delete the physical file
                                                if (
                                                    file_exists(
                                                        $record->file_path
                                                    )
                                                ) {
                                                    unlink($record->file_path);
                                                }

                                                // Try to delete from storage disk as well
                                                try {
                                                    Storage::disk(
                                                        $record->disk
                                                    )->delete(
                                                        $record->file_name
                                                    );
                                                } catch (Exception $e) {
                                                    // Log but don't fail if storage deletion fails
                                                    Log::warning(
                                                        "Failed to delete file from storage disk",
                                                        [
                                                            "file_name" =>
                                                                $record->file_name,
                                                            "disk" =>
                                                                $record->disk,
                                                            "error" => $e->getMessage(),
                                                        ]
                                                    );
                                                }

                                                // Delete the database record
                                                $record->delete();

                                                Notification::make()
                                                    ->title("Resource Deleted")
                                                    ->body(
                                                        "The resource has been successfully deleted."
                                                    )
                                                    ->success()
                                                    ->send();
                                            } catch (Exception $e) {
                                                Notification::make()
                                                    ->title("Deletion Failed")
                                                    ->body(
                                                        "Failed to delete the resource: " .
                                                            $e->getMessage()
                                                    )
                                                    ->danger()
                                                    ->send();
                                            }
                                        }),
                                ])->columnSpan(1),
                            ])
                            ->columnSpanFull()
                            ->columns(6)
                            ->contained(false)
                            ->extraAttributes([
                                "class" => "overflow-x-auto",
                                "style" =>
                                    "min-width: 100%; white-space: nowrap;",
                            ]),
                    ]),
            ])
            ->columns(4);
    }

    public function getTitle(): string
    {
        return "Student Enrollment Details";
    }

    public function getContentFooter(): ?\Illuminate\Contracts\Support\Htmlable
    {
        return null;
    }

    protected function getHeaderActions(): array
    {
        return [
            // Primary Actions
            Actions\Action::make("verifyAsHeadDept")
                ->label("Verify as Dept Head")
                ->icon("heroicon-o-check")
                ->color("primary")
                ->visible(
                    fn(StudentEnrollment $record): bool => $record->status ===
                        EnrollStat::Pending->value &&
                        (Auth::user()->can(
                            "verify_by_head_dept_guest::enrollment"
                        ) ||
                            Auth::user()->hasRole("super_admin"))
                )
                ->form(function (): array {
                    $generalSettings = GeneralSetting::first();
                    if ($generalSettings?->enable_signatures === true) {
                        return [
                            SignaturePad::make("signature")
                                ->label(__("Sign here"))
                                ->dotSize(2.0)
                                ->lineMinWidth(0.5)
                                ->lineMaxWidth(2.5)
                                ->throttle(16)
                                ->minDistance(5)
                                ->velocityFilterWeight(0.7)
                                ->filename("autograph")
                                ->downloadable()
                                ->downloadableFormats([
                                    DownloadableFormat::PNG,
                                    DownloadableFormat::JPG,
                                ])
                                ->backgroundColor("rgba(0,0,0,0)")
                                ->backgroundColorOnDark("#000")
                                ->exportBackgroundColor("#fff")
                                ->penColor("#000")
                                ->penColorOnDark("#fff")
                                ->exportPenColor("#000")
                                ->downloadActionDropdownPlacement("center-end"),
                        ];
                    }

                    return [];
                })
                ->requiresConfirmation()
                ->action(function (
                    StudentEnrollment $record,
                    array $data,
                    EnrollmentService $enrollmentService
                ): void {
                    $signature = $data["signature"] ?? null;
                    $success = $enrollmentService->verifyByHeadDept(
                        $record,
                        $signature
                    );
                    if (!$success) {
                        $this->halt();
                    }
                    $this->refreshFormData([
                        "status",
                        "signature.depthead_signature",
                    ]);
                }),

            Actions\Action::make("verifyAsCashier")
                ->label("Enroll This Student")
                ->icon("heroicon-o-check")
                ->color("success")
                ->modalHeading("Pending Payment")
                ->modalDescription(
                    "This student has not yet paid the down payment. Please enter the amount of the down payment."
                )
                ->form(function ($record): array {
                    $formComponents = [
                        KeyValue::make("settlements")
                            ->label("Settlements")
                            ->columnSpanFull()
                            ->helperText(
                                "Enter the settlements for the following."
                            )
                            ->default([
                                "registration_fee" => 0,
                                "tuition_fee" =>
                                    $record->studentTuition?->downpayment ?? 0,
                                "miscelanous_fee" => 0,
                                "diploma_or_certificate" => 0,
                                "transcript_of_records" => 0,
                                "certification" => 0,
                                "special_exam" => 0,
                                "others" => 0,
                            ])
                            ->reorderable()
                            ->editableKeys(false)
                            ->keyLabel("Particulars")
                            ->deletable(false)
                            ->addable(false)
                            ->valueLabel("Ammounts")
                            ->required(),
                        TextInput::make("invoicenumber")
                            ->label("Invoice Number")
                            ->required(),
                    ];
                    $generalSettings = GeneralSetting::first();
                    if ($generalSettings?->enable_signatures === true) {
                        $formComponents[] = SignaturePad::make("signature")
                            ->label(__("Sign here"))
                            ->dotSize(2.0)
                            ->lineMinWidth(0.5)
                            ->lineMaxWidth(2.5)
                            ->throttle(16)
                            ->minDistance(5)
                            ->velocityFilterWeight(0.7)
                            ->filename("autograph")
                            ->downloadable()
                            ->downloadableFormats([
                                DownloadableFormat::PNG,
                                DownloadableFormat::JPG,
                            ])
                            ->backgroundColor("rgba(0,0,0,0)")
                            ->backgroundColorOnDark("#000")
                            ->exportBackgroundColor("#fff")
                            ->penColor("#000")
                            ->penColorOnDark("#fff")
                            ->exportPenColor("#000")
                            ->downloadActionDropdownPlacement("center-end");
                    }

                    return $formComponents;
                })
                ->visible(
                    fn(StudentEnrollment $record): bool => ($record->status ===
                        EnrollStat::VerifiedByCashier->value ||
                        $record->status ===
                            EnrollStat::VerifiedByDeptHead->value) &&
                        (Auth::user()->can(
                            "verify_by_cashier_guest::enrollment"
                        ) ||
                            Auth::user()->hasRole("super_admin"))
                )
                ->action(function (
                    StudentEnrollment $record,
                    array $data,
                    EnrollmentService $enrollmentService
                ) {
                    $success = $enrollmentService->verifyByCashier(
                        $record,
                        $data
                    );
                    if ($success) {
                        return redirect()->route(
                            "filament.admin.resources.students.index"
                        );
                    }
                    $this->halt();
                }),

            // Dropdown for other actions
            ActionGroup::make([
                Actions\EditAction::make(),
                Actions\Action::make("retryClassEnrollment")
                    ->label("Retry Class Enrollment")
                    ->icon("heroicon-o-arrow-path")
                    ->color("success")
                    ->requiresConfirmation()
                    ->modalHeading("Retry Class Enrollment?")
                    ->modalDescription(
                        "This will attempt to re-enroll the student in all classes for the subjects in this enrollment. Force enrollment is enabled by default to override maximum class size limits."
                    )
                    ->form([
                        \Filament\Forms\Components\Toggle::make(
                            "force_enrollment"
                        )
                            ->label("Force Enrollment")
                            ->helperText(
                                "Override maximum class size limits when enrolling"
                            )
                            ->default(true),
                    ])
                    ->action(function (
                        array $data,
                        StudentEnrollment $record
                    ): void {
                        $originalConfigValue = config(
                            "enrollment.force_enroll_when_full"
                        );
                        if ($data["force_enrollment"]) {
                            config([
                                "enrollment.force_enroll_when_full" => true,
                            ]);
                        }
                        try {
                            $student = $record->student;
                            if (!$student) {
                                Notification::make()
                                    ->danger()
                                    ->title("Student Not Found")
                                    ->body(
                                        "The student associated with this enrollment could not be found."
                                    )
                                    ->send();

                                return;
                            }
                            $student->autoEnrollInClasses($record->id);
                            Notification::make()
                                ->success()
                                ->title("Enrollment Retry Complete")
                                ->body(
                                    "The system has attempted to enroll the student in all classes. Check the notification for results."
                                )
                                ->send();
                        } catch (Exception $e) {
                            Notification::make()
                                ->danger()
                                ->title("Enrollment Retry Failed")
                                ->body("An error occurred: " . $e->getMessage())
                                ->send();
                        } finally {
                            if ($data["force_enrollment"]) {
                                config([
                                    "enrollment.force_enroll_when_full" => $originalConfigValue,
                                ]);
                            }
                        }
                    }),

                Actions\Action::make("undoCashierVerification")
                    ->label("Undo Cashier Verification")
                    ->icon("heroicon-o-arrow-uturn-left")
                    ->color("warning")
                    ->requiresConfirmation()
                    ->modalHeading("Undo Cashier Verification?")
                    ->modalDescription(
                        'This will restore the enrollment and revert the status to "Verified By Dept Head". Financial transactions will NOT be automatically reversed and may require manual correction. Proceed?'
                    )
                    ->visible(function (StudentEnrollment $record): bool {
                        $currentRecord = StudentEnrollment::withTrashed()->find(
                            $record->id
                        );

                        return ($currentRecord->trashed() ||
                            $currentRecord->status ===
                                EnrollStat::VerifiedByCashier->value) &&
                            (Auth::user()->can(
                                "verify_by_cashier_guest::enrollment"
                            ) ||
                                Auth::user()->hasRole("super_admin"));
                    })
                    ->action(function (
                        StudentEnrollment $record,
                        EnrollmentService $enrollmentService
                    ): void {
                        $success = $enrollmentService->undoCashierVerification(
                            $record->id
                        );
                        if (!$success) {
                            $this->halt();
                        }
                        $this->refreshFormData([
                            "status",
                            "signature.cashier_signature",
                        ]);
                        $this->dispatch("refresh");
                    })
                    ->disabled(
                        fn(
                            StudentEnrollment $record
                        ): bool => !$record->trashed() &&
                            $record->status !==
                                EnrollStat::VerifiedByCashier->value
                    ),

                Actions\Action::make("undoHeadDeptVerification")
                    ->label("Undo Head Dept Verification")
                    ->icon("heroicon-o-arrow-uturn-left")
                    ->color("warning")
                    ->requiresConfirmation()
                    ->modalHeading("Undo Head Dept Verification?")
                    ->modalDescription(
                        'This will revert the status to "Pending" and remove the Head Dept signature. Proceed?'
                    )
                    ->visible(
                        fn(
                            StudentEnrollment $record
                        ): bool => $record->status ===
                            EnrollStat::VerifiedByDeptHead->value &&
                            (Auth::user()->can(
                                "verify_by_head_dept_guest::enrollment"
                            ) ||
                                Auth::user()->hasRole("super_admin"))
                    )
                    ->action(function (
                        StudentEnrollment $record,
                        EnrollmentService $enrollmentService
                    ): void {
                        $success = $enrollmentService->undoHeadDeptVerification(
                            $record
                        );
                        if (!$success) {
                            $this->halt();
                        }
                        $this->refreshFormData([
                            "status",
                            "signature.depthead_signature",
                        ]);
                    }),

                Actions\Action::make("Resend Assessment")
                    ->label("Resend Assessment Notification")
                    ->icon("heroicon-o-envelope")
                    ->color("warning")
                    ->visible(
                        fn(
                            StudentEnrollment $record
                        ): bool => $record->status ===
                            EnrollStat::VerifiedByCashier->value &&
                            !empty($record->student->email) &&
                            (Auth::user()->can(
                                "verify_by_cashier_guest::enrollment"
                            ) ||
                                Auth::user()->hasRole("super_admin"))
                    )
                    ->requiresConfirmation()
                    ->modalHeading("Resend Assessment Notification")
                    ->modalDescription(
                        fn(
                            StudentEnrollment $record
                        ): string => "Are you sure you want to resend the assessment notification to {$record->student?->first_name} {$record->student?->last_name} ({$record->student?->email})?\n\nThis will generate a new PDF assessment form and send it via email."
                    )
                    ->modalSubmitActionLabel("Yes, Resend Assessment")
                    ->modalIcon("heroicon-o-envelope")
                    ->action(function (
                        StudentEnrollment $record,
                        EnrollmentService $enrollmentService
                    ): void {
                        $result = $enrollmentService->resendAssessmentNotification(
                            $record
                        );
                        if ($result["success"]) {
                            Notification::make()
                                ->title("Assessment Resend Queued")
                                ->success()
                                ->body(
                                    "Assessment notification has been queued for {$result["student_name"]}. The process is running in the background and you will receive a notification when completed."
                                )
                                ->send();
                        } else {
                            Notification::make()
                                ->title("Assessment Resend Failed")
                                ->danger()
                                ->body($result["message"])
                                ->send();
                            $this->halt();
                        }
                    }),

                Actions\Action::make("create_new_assessment_pdf")
                    ->label("Create New Assessment PDF")
                    ->icon("heroicon-o-document-plus")
                    ->color("info")
                    ->visible(
                        fn(
                            StudentEnrollment $record
                        ): bool => $record->status === "Verified By Cashier"
                    )
                    ->requiresConfirmation()
                    ->modalHeading("Create New Assessment PDF")
                    ->modalDescription(
                        "This will generate a new assessment PDF for this student enrollment without overwriting the existing one. This is useful when there are issues with class schedules not showing up correctly in the PDF."
                    )
                    ->action(function (StudentEnrollment $record): void {
                        try {
                            // Generate unique job ID
                            $jobId = uniqid("pdf_", true);

                            // Dispatch the PDF generation job with a flag to create new file
                            \App\Jobs\GenerateAssessmentPdfJob::dispatch(
                                $record,
                                $jobId,
                                true
                            );

                            Notification::make()
                                ->title("New PDF Generation Queued")
                                ->body(
                                    "A new assessment PDF generation has been queued and will be processed shortly. Super admin users will receive a notification when it's complete."
                                )
                                ->success()
                                ->send();

                            Log::info(
                                "New assessment PDF generation queued for enrollment {$record->id}",
                                [
                                    "enrollment_id" => $record->id,
                                    "student_id" => $record->student_id,
                                    "student_name" =>
                                        $record->student?->full_name ??
                                        "Unknown",
                                    "create_new" => true,
                                    "job_id" => $jobId,
                                ]
                            );
                        } catch (Exception $e) {
                            Notification::make()
                                ->title("PDF Generation Failed")
                                ->body(
                                    "Failed to queue PDF generation: " .
                                        $e->getMessage()
                                )
                                ->danger()
                                ->send();

                            Log::error(
                                "Failed to queue new assessment PDF generation for enrollment {$record->id}",
                                [
                                    "enrollment_id" => $record->id,
                                    "error" => $e->getMessage(),
                                    "trace" => $e->getTraceAsString(),
                                ]
                            );
                        }
                    }),

                Actions\Action::make("View Assessment")
                    ->label("View Assessment")
                    ->icon("heroicon-o-eye")
                    ->color("primary")
                    ->url(
                        fn(): string => route("assessment.download", [
                            "record" => $this->getRecord()->id,
                        ])
                    )
                    ->openUrlInNewTab(true),
            ])
                ->label("More Options")
                ->icon("heroicon-m-ellipsis-vertical")
                ->size(\Filament\Support\Enums\ActionSize::Small)
                ->color("gray")
                ->button(),
        ];
    }

    protected function getViewData(): array
    {
        return parent::getViewData();
    }

    protected function getFooterWidgets(): array
    {
        return [];
    }

    protected function getHeaderWidgets(): array
    {
        return [];
    }
}
